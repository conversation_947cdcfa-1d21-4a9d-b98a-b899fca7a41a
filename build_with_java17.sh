#!/bin/bash

echo "🔧 Building with Java 17 compatibility fixes..."

cd "/Users/<USER>/Desktop/android project/Luatdansu copy 4"

echo "🧹 Cleaning all caches..."
rm -rf app/build .gradle ~/.gradle/caches

echo "📋 Current Java version:"
java -version

echo ""
echo "🔧 Fixes applied:"
echo "- gradle.properties: Disabled daemon, set Java 17 target"
echo "- build.gradle: Updated to JavaVersion.VERSION_17"
echo "- Force release = 17 for JavaCompile tasks"
echo "- Clean all Gradle caches"

echo ""
echo "📱 Attempting build with Java compatibility fixes..."

# Try different approaches
echo "🎯 Approach 1: Build with --no-daemon and Java 17 settings"
./gradlew clean assembleDebug --no-daemon --warning-mode all

if [ $? -eq 0 ]; then
    echo "✅ Build successful with Java 17 compatibility!"
    echo "📱 Installing APK..."
    adb install -r app/build/outputs/apk/debug/app-debug.apk
    
    if [ $? -eq 0 ]; then
        echo "✅ APK installed successfully!"
        echo "🚀 Starting app..."
        adb shell pm clear com.oriondev.luatdansu
        adb shell am start -n com.oriondev.luatdansu/.MainActivity
        echo ""
        echo "🎉 App started successfully!"
        echo ""
        echo "📋 Test chapters filtering:"
        echo "1. Open navigation drawer"
        echo "2. Tap 'Danh sách chương'"
        echo "3. Should see only chapters with valid content"
    else
        echo "❌ Failed to install APK"
    fi
else
    echo "❌ Build still failed"
    echo ""
    echo "🔧 Alternative solutions:"
    echo "1. Use Android Studio (recommended)"
    echo "2. Install Java 17: brew install openjdk@17"
    echo "3. Set JAVA_HOME manually"
    echo ""
    echo "📋 Manual JAVA_HOME setup:"
    echo 'export JAVA_HOME=/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home'
    echo 'export PATH=$JAVA_HOME/bin:$PATH'
    echo ""
    echo "✅ Code is ready - just need compatible Java version!"
fi

#!/bin/bash

echo "🔄 JSON DATA SYNC TEST"
echo "====================="

cd "/Users/<USER>/Desktop/android project/Luatdansu copy 4"

echo ""
echo "📊 CHECKING JSON FILE:"
echo "---------------------"
json_file="app/src/main/assets/data/luat_dan_su.json"

if [ -f "$json_file" ]; then
    echo "✅ JSON file exists: $json_file"
    file_size=$(ls -lh "$json_file" | awk '{print $5}')
    echo "   Size: $file_size"
    
    # Check last modified time
    last_modified=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$json_file")
    echo "   Last modified: $last_modified"
    
    # Count chapters in JSON
    chapter_count=$(grep -c '"type": "chapter"' "$json_file")
    echo "   Total chapters in JSON: $chapter_count"
    
    # Count uppercase titles
    uppercase_count=$(grep -A 1 '"type": "chapter"' "$json_file" | grep '"title":' | grep -E '[A-Z]{5,}' | wc -l | tr -d ' ')
    echo "   Chapters with uppercase titles: $uppercase_count"
    
    # Count "của Bộ luật này" entries
    invalid_count=$(grep -A 1 '"type": "chapter"' "$json_file" | grep '"title":' | grep -i 'của bộ luật' | wc -l | tr -d ' ')
    echo "   Invalid 'của Bộ luật này' entries: $invalid_count"
    
else
    echo "❌ JSON file not found: $json_file"
    exit 1
fi

echo ""
echo "🔧 SYNC MECHANISM ANALYSIS:"
echo "---------------------------"
echo "1. ✅ App loads JSON only when database is empty"
echo "2. ✅ Added forceReloadDataFromJson() method"
echo "3. ✅ Added 'Tải lại dữ liệu JSON' menu option"
echo "4. ✅ Clear database before reloading"
echo "5. ✅ Progress indicator during reload"

echo ""
echo "🎯 SOLUTION IMPLEMENTED:"
echo "------------------------"
echo "• MainActivity.forceReloadDataFromJson()"
echo "• LawRepository.deleteAllLawItems()"
echo "• Navigation menu: 'Tải lại dữ liệu JSON'"
echo "• Clear → Wait → Reload → Refresh UI"

echo ""
echo "📱 BUILD AND TEST:"
echo "-----------------"
echo "Building APK with sync fix..."

# Build the APK
./gradlew assembleDebug --no-daemon --quiet

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    
    if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
        apk_size=$(ls -lh app/build/outputs/apk/debug/app-debug.apk | awk '{print $5}')
        echo "   APK size: $apk_size"
        echo "   Location: app/build/outputs/apk/debug/app-debug.apk"
    fi
else
    echo "❌ Build failed!"
    exit 1
fi

echo ""
echo "🧪 TESTING INSTRUCTIONS:"
echo "------------------------"
echo "1. Install the updated APK:"
echo "   adb install app/build/outputs/apk/debug/app-debug.apk"
echo ""
echo "2. Open app and check current data"
echo ""
echo "3. Update your JSON file with new data"
echo ""
echo "4. In app: Open navigation drawer → 'Tải lại dữ liệu JSON'"
echo ""
echo "5. Verify the data has been updated:"
echo "   • Go to 'Danh sách chương'"
echo "   • Check if new chapters appear"
echo "   • Verify filtering still works"
echo "   • Confirm only uppercase titles show"

echo ""
echo "🔄 HOW IT WORKS:"
echo "---------------"
echo "1. User taps 'Tải lại dữ liệu JSON'"
echo "2. App shows progress bar"
echo "3. Clear all existing data from database"
echo "4. Load fresh data from JSON assets"
echo "5. Apply new filtering logic"
echo "6. Refresh UI with updated data"
echo "7. Show success message"

echo ""
echo "✅ BENEFITS:"
echo "------------"
echo "• No need to uninstall/reinstall app"
echo "• Instant sync with updated JSON"
echo "• Preserves user settings and bookmarks"
echo "• Easy to use from navigation menu"
echo "• Progress feedback during reload"

echo ""
echo "🎉 SUMMARY:"
echo "----------"
echo "Added force reload mechanism to sync app data"
echo "with updated JSON file without reinstalling!"
echo ""
echo "Now you can update JSON → tap reload → see changes!"

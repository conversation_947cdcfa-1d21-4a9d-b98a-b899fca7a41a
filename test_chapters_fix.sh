#!/bin/bash

echo "🔧 Testing chapters list fix..."

cd "/Users/<USER>/Desktop/android project/Luatdansu copy 4"

echo "🧹 Cleaning build cache..."
rm -rf app/build

echo "📱 Building with chapters fix..."
echo ""
echo "✅ Fixed issues:"
echo "- ChaptersActivity now loads from database instead of hardcoded data"
echo "- Added ProgressBar and EmptyView to layout"
echo "- Connected to MainViewModel for real data"
echo "- Added proper observers for LiveData"
echo "- Convert LawEntity to Chapter objects"
echo ""
echo "🎯 Expected result:"
echo "- All chapters from Civil Code will be displayed"
echo "- Not just 10 hardcoded chapters"
echo "- Loading states handled properly"

# Try to build
./gradlew assembleDebug --no-daemon

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📱 Installing APK..."
    adb install -r app/build/outputs/apk/debug/app-debug.apk
    
    if [ $? -eq 0 ]; then
        echo "✅ APK installed successfully!"
        echo "🚀 Starting app..."
        adb shell pm clear com.oriondev.luatdansu
        adb shell am start -n com.oriondev.luatdansu/.MainActivity
        echo ""
        echo "🎉 App started!"
        echo ""
        echo "📋 Test chapters list:"
        echo "1. Open navigation drawer"
        echo "2. Tap 'Danh sách chương'"
        echo "3. Should see ALL chapters from database"
        echo "4. Not just 10 hardcoded chapters"
    else
        echo "❌ Failed to install APK"
    fi
else
    echo "❌ Build failed"
    echo "📋 If Java 25 issue persists, use Android Studio"
    echo ""
    echo "✅ Chapters fix is ready in code!"
fi

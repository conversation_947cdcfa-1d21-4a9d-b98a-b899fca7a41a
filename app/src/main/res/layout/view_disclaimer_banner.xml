<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- Expanded Content -->
    <LinearLayout
        android:id="@+id/expandedContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp">

            <!-- Warning Icon -->
            <ImageView
                android:id="@+id/disclaimerIcon"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_warning"
                android:contentDescription="Warning icon"
                android:tint="@color/orange_accent" />

            <!-- Title -->
            <TextView
                android:id="@+id/disclaimerTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@id/disclaimerIcon"
                android:layout_toStartOf="@id/disclaimerCloseButton"
                android:layout_centerVertical="true"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="8dp"
                android:text="Tuyên bố miễn trừ trách nhiệm"
                android:textStyle="bold"
                android:textSize="18sp"
                android:textColor="@color/text_primary"
                android:fontFamily="sans-serif-medium" />

            <!-- Close Button -->
            <ImageButton
                android:id="@+id/disclaimerCloseButton"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_close"
                android:contentDescription="Đóng tuyên bố"
                android:tint="@color/text_secondary"
                android:padding="6dp" />

        </RelativeLayout>

        <!-- Content Text -->
        <TextView
            android:id="@+id/disclaimerContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Ứng dụng này không phải là ứng dụng chính thức của chính phủ và không liên kết với bất kỳ cơ quan chính phủ nào. Nội dung được cung cấp chỉ nhằm mục đích tham khảo."
            android:textSize="15sp"
            android:textColor="@color/text_secondary"
            android:lineSpacingExtra="3dp"
            android:fontFamily="sans-serif" />

        <!-- Action Buttons Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <!-- Minimize Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/disclaimerMinimizeButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:text="Thu gọn"
                android:textSize="13sp"
                android:textColor="@color/text_secondary"
                android:backgroundTint="@color/background_light"
                app:cornerRadius="20dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/apple_gray"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <!-- Understand Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/disclaimerUnderstandButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="Hiểu rồi"
                android:textSize="13sp"
                android:textColor="@color/white"
                android:backgroundTint="@color/orange_accent"
                app:cornerRadius="20dp"
                style="@style/Widget.Material3.Button.UnelevatedButton" />

        </LinearLayout>

    </LinearLayout>

    <!-- Minimized Content -->
    <LinearLayout
        android:id="@+id/minimizedContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical"
        android:visibility="gone">

        <!-- Warning Icon (smaller) -->
        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_warning"
            android:contentDescription="Warning icon"
            android:tint="@color/orange_accent" />

        <!-- Minimized Text -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:text="Tuyên bố miễn trừ trách nhiệm"
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- Expand Button -->
        <ImageButton
            android:id="@+id/disclaimerExpandButton"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_expand_more"
            android:contentDescription="Mở rộng tuyên bố"
            android:tint="@color/text_secondary"
            android:padding="4dp" />

    </LinearLayout>

</merge>

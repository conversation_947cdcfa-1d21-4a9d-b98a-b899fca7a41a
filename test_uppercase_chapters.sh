#!/bin/bash

echo "🎯 UPPERCASE CHAPTERS FILTERING TEST"
echo "===================================="

cd "/Users/<USER>/Desktop/android project/Luatdansu copy 4"

echo ""
echo "📊 ANALYZING CHAPTER TITLES IN JSON:"
echo "------------------------------------"

echo ""
echo "🔍 Sample chapter titles from JSON:"
echo "   (Showing first 10 chapters with their titles)"
grep -A 4 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep -E '"number":|"title":' | head -20 | while read line; do
    echo "   $line"
done

echo ""
echo "📋 UPPERCASE TITLES (should be shown):"
echo "   Examples of valid uppercase titles:"
grep -A 4 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep '"title":' | grep -E '[A-Z]{5,}' | head -5 | sed 's/^/   /'

echo ""
echo "📋 LOWERCASE/MIXED TITLES (should be filtered out):"
echo "   Examples of invalid titles:"
grep -A 4 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep '"title":' | grep -i 'của bộ luật' | head -5 | sed 's/^/   /'

echo ""
echo "✅ NEW FILTERING LOGIC:"
echo "----------------------"
echo "1. ✓ Only Roman numeral chapters (I, II, III, etc.)"
echo "2. ✓ Only titles with ≥50% uppercase letters"
echo "3. ✓ Minimum 10 characters length"
echo "4. ✓ Exclude 'của Bộ luật này' and similar phrases"
echo "5. ✓ Extract titles from content if needed"
echo "6. ✓ Smart title validation and formatting"

echo ""
echo "🎯 EXPECTED RESULTS:"
echo "-------------------"
echo "• Only chapters with meaningful UPPERCASE titles"
echo "• No 'của Bộ luật này;' entries"
echo "• No short or meaningless descriptions"
echo "• Clean, professional chapter list"

echo ""
echo "🔧 TECHNICAL IMPLEMENTATION:"
echo "----------------------------"
echo "• isValidChapterTitle() - checks uppercase percentage"
echo "• extractTitleFromContent() - extracts from content"
echo "• formatDescription() - cleans and formats text"
echo "• Enhanced filtering in hasValidContent()"

echo ""
echo "📱 BUILD STATUS:"
echo "---------------"
if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
    echo "✅ APK built successfully!"
    apk_size=$(ls -lh app/build/outputs/apk/debug/app-debug.apk | awk '{print $5}')
    echo "   Size: $apk_size"
    echo "   Location: app/build/outputs/apk/debug/app-debug.apk"
else
    echo "❌ APK not found!"
    exit 1
fi

echo ""
echo "🧪 TESTING INSTRUCTIONS:"
echo "------------------------"
echo "1. Install the updated APK:"
echo "   adb install app/build/outputs/apk/debug/app-debug.apk"
echo ""
echo "2. Open app → 'Danh sách chương'"
echo ""
echo "3. Verify the results match your screenshot requirements:"
echo "   ✓ Only chapters like 'TRANH TỤNG TẠI PHIÊN TÒA PHÚC THẨM'"
echo "   ✓ No entries like 'của Bộ luật này;'"
echo "   ✓ All titles are meaningful and uppercase"
echo "   ✓ Clean, professional appearance"

echo ""
echo "🎉 SUMMARY:"
echo "----------"
echo "Enhanced filtering to show only chapters with meaningful"
echo "uppercase titles, eliminating low-quality entries."
echo "This should match exactly what you want in your screenshot!"

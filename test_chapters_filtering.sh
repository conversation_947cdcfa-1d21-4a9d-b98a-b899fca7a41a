#!/bin/bash

echo "🔧 Testing chapters filtering fix..."

cd "/Users/<USER>/Desktop/android project/Luatdansu copy 4"

echo "📊 Analyzing JSON data structure..."
echo ""

echo "🔍 All chapter entries in JSON:"
grep -A 3 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep -E '"number":|"title":' | head -20

echo ""
echo "📋 Roman numeral chapters (should be displayed):"
grep -A 3 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep -E '"number": "[IVX]+"' | head -10

echo ""
echo "📋 Numeric chapters (should be filtered out - these are sections, not chapters):"
grep -A 3 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep -E '"number": "[0-9]+"' | head -10

echo ""
echo "✅ Fixed issues:"
echo "- Added isRomanNumeral() method to filter only real chapters"
echo "- Only chapters with Roman numerals (I, II, III, IV, etc.) will be shown"
echo "- Numeric entries (1, 2, 3, etc.) are sections/subsections, not chapters"
echo "- Improved content validation to ensure chapters have meaningful titles"
echo ""

echo "🎯 Expected result:"
echo "- Only chapters with Roman numerals should appear in the list"
echo "- No more duplicate or invalid chapters"
echo "- Clean, organized chapter list"
echo ""

echo "📱 Building app to test..."
./gradlew assembleDebug --no-daemon --quiet

if [ $? -eq 0 ]; then
    echo "✅ Build successful! App is ready for testing."
    echo ""
    echo "🧪 To test:"
    echo "1. Install the APK on your device"
    echo "2. Open the app and go to 'Danh sách chương'"
    echo "3. Verify only Roman numeral chapters are shown"
    echo "4. Check that no duplicate or invalid entries appear"
else
    echo "❌ Build failed!"
fi

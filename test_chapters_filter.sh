#!/bin/bash

echo "🔧 Testing chapters content filtering..."

cd "/Users/<USER>/Desktop/android project/Luatdansu copy 4"

echo "🧹 Cleaning build cache..."
rm -rf app/build

echo "📱 Building with chapters content filtering..."
echo ""
echo "✅ New filtering logic:"
echo "- Only show chapters with valid content"
echo "- Filter out empty or invalid chapters"
echo "- Better title and description formatting"
echo "- Debug logging to track filtering"
echo ""
echo "🎯 Expected result:"
echo "- Only chapters with meaningful content displayed"
echo "- No empty or placeholder chapters"
echo "- Better formatted titles and descriptions"
echo "- Check logcat for filtering details"

# Try to build
./gradlew assembleDebug --no-daemon

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📱 Installing APK..."
    adb install -r app/build/outputs/apk/debug/app-debug.apk
    
    if [ $? -eq 0 ]; then
        echo "✅ APK installed successfully!"
        echo "🚀 Starting app..."
        adb shell pm clear com.oriondev.luatdansu
        adb shell am start -n com.oriondev.luatdansu/.MainActivity
        echo ""
        echo "🎉 App started!"
        echo ""
        echo "📋 Test chapters filtering:"
        echo "1. Open navigation drawer"
        echo "2. Tap '<PERSON><PERSON> s<PERSON>ch ch<PERSON>'"
        echo "3. Should see only chapters with valid content"
        echo "4. Check logcat: adb logcat | grep ChaptersActivity"
        echo ""
        echo "🔍 Debug commands:"
        echo "adb logcat | grep 'ChaptersActivity'"
        echo "adb logcat | grep 'Raw chapter'"
        echo "adb logcat | grep 'Valid'"
    else
        echo "❌ Failed to install APK"
    fi
else
    echo "❌ Build failed"
    echo "📋 If Java 25 issue persists, use Android Studio"
    echo ""
    echo "✅ Chapters filtering is ready in code!"
fi

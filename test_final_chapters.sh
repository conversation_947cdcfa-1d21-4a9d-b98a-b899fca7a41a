#!/bin/bash

echo "🎉 FINAL TEST: Chapters List Filtering Fix"
echo "=========================================="

cd "/Users/<USER>/Desktop/android project/Luatdansu copy 4"

echo ""
echo "📊 ANALYSIS OF JSON DATA:"
echo "------------------------"

echo ""
echo "🔍 Total chapter entries in JSON:"
total_chapters=$(grep -c '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json)
echo "   Total: $total_chapters entries"

echo ""
echo "📋 Roman numeral chapters (VALID - will be shown):"
roman_chapters=$(grep -A 3 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep -E '"number": "[IVX]+"' | wc -l)
echo "   Count: $roman_chapters chapters"
echo "   Examples:"
grep -A 3 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep -E '"number": "[IVX]+"' | head -5 | sed 's/^/   /'

echo ""
echo "📋 Numeric chapters (INVALID - will be filtered out):"
numeric_chapters=$(grep -A 3 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep -E '"number": "[0-9]+"' | wc -l)
echo "   Count: $numeric_chapters sections (these are subsections, not chapters)"
echo "   Examples:"
grep -A 3 '"type": "chapter"' app/src/main/assets/data/luat_dan_su.json | grep -E '"number": "[0-9]+"' | head -5 | sed 's/^/   /'

echo ""
echo "✅ FIXES IMPLEMENTED:"
echo "--------------------"
echo "1. ✓ Added isRomanNumeral() method with proper regex"
echo "2. ✓ Enhanced hasValidContent() to filter only Roman numeral chapters"
echo "3. ✓ Improved content validation for meaningful titles"
echo "4. ✓ Fixed regex to support full range of Roman numerals (I-XXXIX)"
echo "5. ✓ Added proper logging for debugging"

echo ""
echo "🎯 EXPECTED RESULTS:"
echo "-------------------"
echo "• Only $roman_chapters chapters with Roman numerals will be displayed"
echo "• No duplicate or invalid entries"
echo "• Clean, organized chapter list"
echo "• No more entries like 'Chương VII của Bộ luật này;'"

echo ""
echo "🔧 TECHNICAL DETAILS:"
echo "--------------------"
echo "• Regex pattern: ^(XL|L?X{0,3})(IX|IV|V?I{0,3})$"
echo "• Supports Roman numerals from I to XXXIX"
echo "• Filters out numeric sections (1, 2, 3, etc.)"
echo "• Validates content length and meaningfulness"

echo ""
echo "📱 BUILD STATUS:"
echo "---------------"
if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
    echo "✅ APK built successfully!"
    apk_size=$(ls -lh app/build/outputs/apk/debug/app-debug.apk | awk '{print $5}')
    echo "   Size: $apk_size"
    echo "   Location: app/build/outputs/apk/debug/app-debug.apk"
else
    echo "❌ APK not found, building now..."
    ./gradlew assembleDebug --no-daemon --quiet
    if [ $? -eq 0 ]; then
        echo "✅ Build successful!"
    else
        echo "❌ Build failed!"
        exit 1
    fi
fi

echo ""
echo "🧪 TESTING INSTRUCTIONS:"
echo "------------------------"
echo "1. Install the APK on your Android device:"
echo "   adb install app/build/outputs/apk/debug/app-debug.apk"
echo ""
echo "2. Open the app and navigate to 'Danh sách chương'"
echo ""
echo "3. Verify the following:"
echo "   ✓ Only chapters with Roman numerals are shown (II, III, IV, etc.)"
echo "   ✓ No duplicate entries"
echo "   ✓ No invalid entries like 'của Bộ luật này;'"
echo "   ✓ Clean, meaningful chapter titles"
echo "   ✓ Approximately $roman_chapters chapters total"

echo ""
echo "🎉 SUMMARY:"
echo "----------"
echo "The chapters list has been fixed to show only valid chapters with"
echo "Roman numerals, filtering out subsections and invalid entries."
echo "This should resolve the display issues shown in your screenshot."
